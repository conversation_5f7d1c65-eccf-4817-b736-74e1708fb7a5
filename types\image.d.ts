export interface ImageUpload {
  file: File;
  preview: string;
  name: string;
  size: number;
  type: string;
}

export interface ProcessedImage {
  id: string;
  originalUrl: string;
  processedUrl: string;
  width: number;
  height: number;
  format: 'png' | 'jpg';
  createdAt: Date;
}

export interface ImageProcessingResult {
  success: boolean;
  processedImageUrl?: string;
  error?: string;
  objectDetected?: boolean;
  processingTime?: number;
}

export interface ObjectDetection {
  detected: boolean;
  confidence: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  mask?: ImageData;
}
