# Text-Behind

A modern web application that allows users to upload images and automatically add customizable text behind detected objects using AI-powered object detection.

## Features

- **AI-Powered Object Detection**: Automatically detects humans and objects in uploaded images
- **Customizable Text**: Edit text content, color, font, and position
- **Real-time Preview**: See changes instantly as you customize
- **Multiple Formats**: Download in PNG or JPG format
- **User Authentication**: Secure authentication with Clerk
- **Modern UI**: Clean, minimalist, and professional design

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS v4
- **Authentication**: Clerk
- **Database**: MongoDB with Mongoose
- **Image Processing**: Sharp
- **AI/ML**: TensorFlow.js (BodyPix/PoseNet)
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- MongoDB database
- Clerk account for authentication

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd textbehind
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Fill in your environment variables:
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`
- `CLERK_SECRET_KEY`
- `MONGODB_URI`

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
├── app/                    # Next.js App Router
├── components/             # Reusable React components
├── lib/                    # Server-side utilities
├── utils/                  # Client-side utilities
├── types/                  # TypeScript type definitions
├── constants/              # Application constants
├── db/                     # Database models and connection
├── hooks/                  # Custom React hooks
├── services/               # Business logic services
└── public/                 # Static assets
```

## Development Stages

This project is being built in stages:

1. ✅ **Project Setup & Basic Structure**
2. 🔄 **Authentication Setup with Clerk**
3. ⏳ **Basic UI Components & Layout**
4. ⏳ **Image Upload Functionality**
5. ⏳ **Basic Image Processing API**
6. ⏳ **Image Editor Interface**
7. ⏳ **Download Functionality**
8. ⏳ **Error Handling & Toast Notifications**
9. ⏳ **AI Object Detection Integration**
10. ⏳ **Database Setup & Configuration**
11. ⏳ **Styling & Polish**
12. ⏳ **Testing & Optimization**

## Contributing

This project follows a structured development approach. Each stage is tested before moving to the next.

## License

MIT License
