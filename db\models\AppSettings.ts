import mongoose, { Schema, Document } from 'mongoose';

export interface IAppSettings extends Document {
  _id: string;
  defaultText: string;
  defaultFontFamily: string;
  defaultFontSize: number;
  defaultTextColor: string;
  defaultTextPositionPreset: string;
  lastModified: Date;
  modifiedBy: string;
}

const AppSettingsSchema: Schema = new Schema({
  _id: {
    type: String,
    default: 'global_settings',
    required: true,
  },
  defaultText: {
    type: String,
    required: true,
    default: 'POV',
  },
  defaultFontFamily: {
    type: String,
    required: true,
    default: 'Inter',
  },
  defaultFontSize: {
    type: Number,
    required: true,
    default: 60,
  },
  defaultTextColor: {
    type: String,
    required: true,
    default: '#FFFFFF',
  },
  defaultTextPositionPreset: {
    type: String,
    required: true,
    default: 'behind-object-center',
  },
  lastModified: {
    type: Date,
    default: Date.now,
  },
  modifiedBy: {
    type: String,
    default: 'system',
  },
}, {
  timestamps: true,
  collection: 'app_settings',
});

// Ensure only one settings document exists
AppSettingsSchema.index({ _id: 1 }, { unique: true });

export default mongoose.models.AppSettings || mongoose.model<IAppSettings>('AppSettings', AppSettingsSchema);
