export const ERROR_MESSAGES = {
  // File upload errors
  FILE_TOO_LARGE: 'File too large. Maximum size is 5MB.',
  INVALID_FILE_TYPE: 'Unsupported file type. Please upload a JPG or PNG image.',
  UPLOAD_FAILED: 'Failed to upload image. Please try again.',
  NO_FILE_SELECTED: 'Please select an image file to upload.',
  
  // Image processing errors
  PROCESSING_FAILED: 'An error occurred during processing. Please try again later.',
  NO_OBJECT_DETECTED: 'No object detected. Please try another image.',
  INVALID_IMAGE_DATA: 'Invalid image data. Please upload a valid image.',
  PROCESSING_TIMEOUT: 'Processing took too long. Please try with a smaller image.',
  
  // Authentication errors
  AUTH_REQUIRED: 'Please sign in to continue.',
  AUTH_FAILED: 'Authentication failed. Please try again.',
  SESSION_EXPIRED: 'Your session has expired. Please sign in again.',
  
  // Network errors
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  API_ERROR: 'API error. Please contact support if this persists.',
  
  // Download errors
  DOWNLOAD_FAILED: 'Failed to download image. Please try again.',
  NO_PROCESSED_IMAGE: 'No processed image available for download.',
  
  // General errors
  UNKNOWN_ERROR: 'An unknown error occurred. Please try again.',
  VALIDATION_ERROR: 'Validation error. Please check your input.',
} as const;

export const SUCCESS_MESSAGES = {
  IMAGE_UPLOADED: 'Image uploaded successfully!',
  IMAGE_PROCESSED: 'Image processed successfully!',
  IMAGE_DOWNLOADED: 'Image downloaded successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!',
} as const;
