'use client';

import { Header } from '@/app/_components/Header';
import { Footer } from '@/app/_components/Footer';
import { Button } from '@/components/ui/Button';
import { ImageUploadForm } from '@/components/ImageUploadForm';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-gray-900 sm:text-6xl">
            Add text behind your photos,{" "}
            <span className="text-blue-600">instantly</span>
          </h2>
          <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
            Upload your images and automatically add customizable text behind detected objects.
            Modern, fast, and professional image editing powered by AI.
          </p>

          {/* Upload Area */}
          <div className="mt-12">
            <ImageUploadForm
              onImageUploaded={(imageData) => {
                console.log('Image uploaded:', imageData);
              }}
            />
          </div>

          {/* Features */}
          <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">AI-Powered Detection</h3>
              <p className="mt-2 text-gray-600">Automatically detects objects in your images for precise text placement.</p>
            </div>
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">Customizable Text</h3>
              <p className="mt-2 text-gray-600">Edit text content, color, font, and position to match your style.</p>
            </div>
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">Instant Download</h3>
              <p className="mt-2 text-gray-600">Download your edited images in PNG or JPG format instantly.</p>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
