export const APP_CONFIG = {
  // Image upload constraints
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB in bytes
  SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png'],
  SUPPORTED_EXTENSIONS: ['.jpg', '.jpeg', '.png'],
  
  // Image processing
  MAX_IMAGE_WIDTH: 2048,
  MAX_IMAGE_HEIGHT: 2048,
  COMPRESSION_QUALITY: 0.9,
  
  // API endpoints
  API_ENDPOINTS: {
    PROCESS_IMAGE: '/api/process-image',
    CLERK_WEBHOOK: '/api/clerk-webhook',
  },
  
  // UI constants
  TOAST_DURATION: 5000, // 5 seconds
  LOADING_TIMEOUT: 30000, // 30 seconds
  
  // Canvas settings
  CANVAS_MAX_WIDTH: 1200,
  CANVAS_MAX_HEIGHT: 800,
} as const;

export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  SIGN_IN: '/sign-in',
  SIGN_UP: '/sign-up',
} as const;
