export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  defaultTextColor: string;
  defaultFontFamily: string;
  defaultFontSize: number;
  preferredImageFormat: 'png' | 'jpg';
}

export interface AuthState {
  isLoaded: boolean;
  isSignedIn: boolean;
  user: User | null;
}
