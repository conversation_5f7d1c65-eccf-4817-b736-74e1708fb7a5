import { TextDefaults, FontOption } from '@/types/text';

export const DEFAULT_TEXT_CONFIG: TextDefaults = {
  content: 'POV',
  color: '#FFFFFF',
  fontFamily: 'Inter',
  fontSize: 60,
  position: 'behind-object-center',
  opacity: 1,
};

export const FONT_OPTIONS: FontOption[] = [
  { name: 'Inter', family: 'Inter, sans-serif', weight: 700, style: 'normal' },
  { name: 'Aria<PERSON>', family: 'Arial, sans-serif', weight: 700, style: 'normal' },
  { name: 'Helvetica', family: 'Helvetica, sans-serif', weight: 700, style: 'normal' },
  { name: 'Robot<PERSON>', family: 'Roboto, sans-serif', weight: 700, style: 'normal' },
  { name: 'Open Sans', family: 'Open Sans, sans-serif', weight: 700, style: 'normal' },
  { name: 'Montserrat', family: 'Montserrat, sans-serif', weight: 700, style: 'normal' },
  { name: 'Poppins', family: 'Poppins, sans-serif', weight: 700, style: 'normal' },
  { name: 'Impact', family: 'Impact, sans-serif', weight: 400, style: 'normal' },
];

export const COLOR_PRESETS = [
  '#FFFFFF', // White
  '#000000', // Black
  '#FF0000', // Red
  '#00FF00', // Green
  '#0000FF', // Blue
  '#FFFF00', // Yellow
  '#FF00FF', // Magenta
  '#00FFFF', // Cyan
  '#FFA500', // Orange
  '#800080', // Purple
  '#FFC0CB', // Pink
  '#A52A2A', // Brown
];

export const POSITION_PRESETS = [
  { name: 'Behind Center', value: 'behind-object-center' },
  { name: 'Behind Left', value: 'behind-object-left' },
  { name: 'Behind Right', value: 'behind-object-right' },
  { name: 'Custom', value: 'custom' },
] as const;
