'use client';

import React, { useRef, useState } from 'react';
import { cn } from '@/utils/cn';
import { Button } from '@/components/ui/Button';
import { Spinner } from '@/components/ui/Spinner';
import { useImageUpload } from '@/hooks/useImageUpload';
import { useToast } from '@/components/ui/ToastProvider';
import { formatFileSize } from '@/utils/helpers';

interface ImageUploadFormProps {
  onImageUploaded?: (imageData: any) => void;
  className?: string;
}

const ImageUploadForm: React.FC<ImageUploadFormProps> = ({
  onImageUploaded,
  className,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const { showSuccess, showError } = useToast();

  const {
    uploadedImage,
    isUploading,
    error,
    uploadProgress,
    handleFileSelect,
    clearImage,
    clearError,
  } = useImageUpload();

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleChooseFile = () => {
    fileInputRef.current?.click();
  };

  const handleClearImage = () => {
    clearImage();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  React.useEffect(() => {
    if (uploadedImage && onImageUploaded) {
      onImageUploaded(uploadedImage);
      showSuccess('Image uploaded successfully!', 'Your image is ready for processing.');
    }
  }, [uploadedImage, onImageUploaded, showSuccess]);

  React.useEffect(() => {
    if (error) {
      showError('Upload failed', error);
    }
  }, [error, showError]);

  return (
    <div className={cn('w-full max-w-2xl mx-auto', className)}>
      {!uploadedImage ? (
        <div
          className={cn(
            'border-2 border-dashed rounded-lg p-12 text-center transition-all duration-200 cursor-pointer',
            isDragOver
              ? 'border-blue-400 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400',
            error && 'border-red-300 bg-red-50'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleChooseFile}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/png,image/jpeg,image/jpg"
            onChange={handleFileInputChange}
            className="hidden"
          />
          
          <div className="space-y-4">
            {isUploading ? (
              <div className="space-y-4">
                <Spinner size="lg" />
                <div>
                  <p className="text-lg font-medium text-gray-900">Uploading...</p>
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{uploadProgress}%</p>
                </div>
              </div>
            ) : (
              <>
                <div className="mx-auto h-12 w-12 text-gray-400">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 48 48" aria-hidden="true">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    />
                  </svg>
                </div>
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    {isDragOver ? 'Drop your image here' : 'Upload your image'}
                  </p>
                  <p className="text-sm text-gray-600">
                    PNG or JPG up to 5MB
                  </p>
                </div>
                <Button variant="primary" size="lg" onClick={handleChooseFile}>
                  Choose File
                </Button>
                <p className="text-xs text-gray-500">
                  or drag and drop your image here
                </p>
              </>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Image Preview */}
          <div className="relative">
            <img
              src={uploadedImage.preview}
              alt="Uploaded preview"
              className="w-full h-auto max-h-96 object-contain rounded-lg border border-gray-200"
            />
            <button
              onClick={handleClearImage}
              className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-2 transition-colors"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Image Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">{uploadedImage.name}</p>
                <p className="text-sm text-gray-600">{formatFileSize(uploadedImage.size)}</p>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleChooseFile}>
                  Change Image
                </Button>
                <Button variant="primary" size="sm">
                  Process Image
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <svg className="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-red-800">{error}</p>
            <button
              onClick={clearError}
              className="ml-auto text-red-400 hover:text-red-600"
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export { ImageUploadForm };
