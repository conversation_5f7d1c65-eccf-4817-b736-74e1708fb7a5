export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  description?: string;
  duration?: number;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export interface AppConfig {
  maxFileSize: number;
  supportedFormats: string[];
  defaultText: string;
  defaultFont: string;
  defaultColor: string;
}

declare global {
  interface Window {
    // Add any global window properties here
  }
}

export {};
