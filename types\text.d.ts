export interface TextCustomization {
  content: string;
  color: string;
  fontFamily: string;
  fontSize: number;
  position: TextPosition;
  opacity: number;
  rotation: number;
}

export interface TextPosition {
  x: number;
  y: number;
  preset?: 'behind-object-center' | 'behind-object-left' | 'behind-object-right' | 'custom';
}

export interface FontOption {
  name: string;
  family: string;
  weight: number;
  style: 'normal' | 'italic';
}

export interface TextDefaults {
  content: string;
  color: string;
  fontFamily: string;
  fontSize: number;
  position: string;
  opacity: number;
}

export interface TextRenderOptions {
  text: TextCustomization;
  imageWidth: number;
  imageHeight: number;
  objectBounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}
