'use client';

import React, { useState } from 'react';
import { cn } from '@/utils/cn';
import { FONT_OPTIONS } from '@/constants/text-defaults';

export interface FontSelectorProps {
  value: string;
  onChange: (fontFamily: string) => void;
  label?: string;
  className?: string;
}

const FontSelector: React.FC<FontSelectorProps> = ({
  value,
  onChange,
  label,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const selectedFont = FONT_OPTIONS.find(font => font.family === value) || FONT_OPTIONS[0];

  const handleFontSelect = (fontFamily: string) => {
    onChange(fontFamily);
    setIsOpen(false);
  };

  return (
    <div className={cn('relative', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      {/* Font Display Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full p-2 border border-gray-300 rounded-md hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <span 
          className="text-sm"
          style={{ fontFamily: selectedFont.family }}
        >
          {selectedFont.name}
        </span>
        <svg
          className={cn(
            'w-4 h-4 transition-transform',
            isOpen ? 'rotate-180' : ''
          )}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Font Options Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
          {FONT_OPTIONS.map((font) => (
            <button
              key={font.name}
              type="button"
              onClick={() => handleFontSelect(font.family)}
              className={cn(
                'w-full px-3 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100',
                selectedFont.family === font.family ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
              )}
              style={{ fontFamily: font.family }}
            >
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{font.name}</span>
                <span 
                  className="text-lg"
                  style={{ 
                    fontFamily: font.family,
                    fontWeight: font.weight,
                    fontStyle: font.style
                  }}
                >
                  Aa
                </span>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export { FontSelector };
