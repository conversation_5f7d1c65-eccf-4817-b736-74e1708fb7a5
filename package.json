{"name": "textbehind", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.25.5", "@types/mongoose": "^5.11.96", "clsx": "^2.1.1", "mongoose": "^8.16.4", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}