import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Text-Behind | Add Text Behind Images with AI",
  description: "Upload your images and automatically add customizable text behind detected objects. Modern, fast, and professional image editing tool.",
  keywords: ["image editing", "text behind image", "AI", "photo editing", "text overlay"],
  authors: [{ name: "Text-Behind Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} font-sans antialiased bg-white text-gray-900`}
      >
        {children}
      </body>
    </html>
  );
}
