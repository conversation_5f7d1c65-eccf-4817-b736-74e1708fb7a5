'use client';

import { useState, useCallback } from 'react';
import { validateImageFile } from '@/utils/validation';
import { fileToBase64 } from '@/utils/helpers';
import { ImageUpload } from '@/types/image';

interface UseImageUploadReturn {
  uploadedImage: ImageUpload | null;
  isUploading: boolean;
  error: string | null;
  uploadProgress: number;
  handleFileSelect: (file: File) => Promise<void>;
  clearImage: () => void;
  clearError: () => void;
}

export function useImageUpload(): UseImageUploadReturn {
  const [uploadedImage, setUploadedImage] = useState<ImageUpload | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleFileSelect = useCallback(async (file: File) => {
    setError(null);
    setUploadProgress(0);

    // Validate the file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file');
      return;
    }

    setIsUploading(true);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      // Convert file to base64 for preview
      const preview = await fileToBase64(file);

      // Create image upload object
      const imageUpload: ImageUpload = {
        file,
        preview,
        name: file.name,
        size: file.size,
        type: file.type,
      };

      // Complete the upload
      clearInterval(progressInterval);
      setUploadProgress(100);
      setUploadedImage(imageUpload);
      
      // Reset progress after a short delay
      setTimeout(() => {
        setUploadProgress(0);
        setIsUploading(false);
      }, 500);

    } catch (err) {
      setError('Failed to process the image. Please try again.');
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, []);

  const clearImage = useCallback(() => {
    setUploadedImage(null);
    setError(null);
    setUploadProgress(0);
    setIsUploading(false);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    uploadedImage,
    isUploading,
    error,
    uploadProgress,
    handleFileSelect,
    clearImage,
    clearError,
  };
}
