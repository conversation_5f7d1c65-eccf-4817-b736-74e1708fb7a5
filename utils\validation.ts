import { APP_CONFIG } from '@/constants/app-config';
import { ERROR_MESSAGES } from '@/constants/error-messages';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validate uploaded image file
 */
export function validateImageFile(file: File): ValidationResult {
  // Check file size
  if (file.size > APP_CONFIG.MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.FILE_TOO_LARGE,
    };
  }

  // Check file type
  if (!APP_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.INVALID_FILE_TYPE,
    };
  }

  // Check file extension
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!APP_CONFIG.SUPPORTED_EXTENSIONS.includes(extension)) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.INVALID_FILE_TYPE,
    };
  }

  return { isValid: true };
}

/**
 * Validate text content
 */
export function validateTextContent(text: string): ValidationResult {
  if (!text || text.trim().length === 0) {
    return {
      isValid: false,
      error: 'Text content cannot be empty',
    };
  }

  if (text.length > 100) {
    return {
      isValid: false,
      error: 'Text content cannot exceed 100 characters',
    };
  }

  return { isValid: true };
}

/**
 * Validate color hex code
 */
export function validateHexColor(color: string): ValidationResult {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  
  if (!hexRegex.test(color)) {
    return {
      isValid: false,
      error: 'Invalid color format. Please use hex format (e.g., #FFFFFF)',
    };
  }

  return { isValid: true };
}

/**
 * Validate font size
 */
export function validateFontSize(size: number): ValidationResult {
  if (size < 10 || size > 200) {
    return {
      isValid: false,
      error: 'Font size must be between 10 and 200 pixels',
    };
  }

  return { isValid: true };
}
